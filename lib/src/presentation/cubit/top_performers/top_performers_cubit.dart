import 'package:hydrated_bloc/hydrated_bloc.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/top_performers.dart';
import '../../../domain/repository/top_performers_repository.dart';

part 'top_performers_state.dart';

class TopPerformersCubit extends HydratedCubit<TopPerformersState> {
  TopPerformersCubit(this._topPerformersRepository)
    : super(TopPerformersInitial());
  final TopPerformersRepository _topPerformersRepository;

  Future<void> getBrokerageTopPerformers() async {
    emit(TopPerformersLoading());
    try {
      final topPerformers = await _topPerformersRepository.getBrokerageTopPerformers();
      emit(TopPerformersLoaded(brokerageTopPerformers: topPerformers));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

    Future<void> getAgentTopPerformers() async {
    emit(TopPerformersLoading());
    try {
      final topPerformers = await _topPerformersRepository.getAgentTopPerformers();
      emit(TopPerformersLoaded(agentTopPerformers: topPerformers));
    } on ApiException catch (e) {
      emit(TopPerformersError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        TopPerformersError(
          message: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }

  @override
  TopPerformersState? fromJson(Map<String, dynamic> json) {
    // TODO: implement fromJson
    throw UnimplementedError();
  }

  @override
  Map<String, dynamic>? toJson(TopPerformersState state) {
    // TODO: implement toJson
    throw UnimplementedError();
  }
}
