import 'package:get_it/get_it.dart';
import '../../data/repository/auth_repository_impl.dart';
import '../../data/repository/user_repository_impl.dart';
import '../../data/repository/top_performers_impl.dart';
import '../../domain/repository/auth_repository.dart';
import '../../domain/repository/user_repository.dart';
import '../../domain/repository/top_performers_repository.dart';
import 'token_storage.dart';

final locator = GetIt.instance;

Future<void> initializeDependencies() async {
  // Register token storage
  locator.registerSingleton<TokenStorage>(TokenStorage.instance);

  // Register repositories
  locator.registerSingleton<AuthRepository>(AuthRepositoryImpl());

  // Register user repository factory
  locator.registerFactory<UserRepository>(() {
    return UserRepositoryImpl();
  });

  // Register top performers repository factory
  locator.registerFactory<TopPerformersRepository>(() {
    return TopPerformersRepositoryImpl();
  });
}
